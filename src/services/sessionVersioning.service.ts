import { Request, Response } from 'express';
import { logger } from '../utils/secureLogger';
import { redis } from '../config/redis';
import { User } from '../entities/User';

/**
 * Session Versioning Service
 * Prevents session fixation attacks by versioning sessions when user privileges change
 * Invalidates old sessions when security-sensitive changes occur
 */
export class SessionVersioningService {
  private static readonly SESSION_VERSION_PREFIX = 'session_version:';
  private static readonly USER_SESSIONS_PREFIX = 'user_sessions:';
  private static readonly SESSION_TTL = 24 * 60 * 60; // 24 hours in seconds

  /**
   * Initialize session versioning for a user
   */
  static async initializeSession(req: Request, user: User): Promise<void> {
    try {
      if (!req.session) {
        throw new Error('Session not available');
      }

      const sessionVersion = await this.getCurrentUserVersion(user.id);
      const sessionId = req.sessionID;
      
      // Set session data
      req.session.userId = user.id;
      req.session.sessionVersion = sessionVersion;
      req.session.userRole = user.role;
      req.session.lastRoleChange = user.updatedAt?.toISOString() || new Date().toISOString();
      req.session.sessionCreatedAt = new Date().toISOString();
      req.session.lastActivity = new Date().toISOString();

      // Track session in Redis
      await this.trackUserSession(user.id, sessionId, sessionVersion);

      logger.info('Session initialized with versioning', {
        userId: user.id,
        sessionId: sessionId.substring(0, 8) + '...',
        sessionVersion,
        userRole: user.role
      });
    } catch (error) {
      logger.error('Failed to initialize session versioning', {
        userId: user.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Update session activity timestamp
   */
  static updateSessionActivity(req: Request): void {
    if (req.session && req.session.userId) {
      req.session.lastActivity = new Date().toISOString();
    }
  }

  /**
   * Validate session version against current user version
   */
  static async validateSessionVersion(req: Request): Promise<boolean> {
    try {
      if (!req.session || !req.session.userId || !req.session.sessionVersion) {
        return false;
      }

      const currentVersion = await this.getCurrentUserVersion(req.session.userId);
      const sessionVersion = req.session.sessionVersion;

      if (sessionVersion !== currentVersion) {
        logger.warn('Session version mismatch detected', {
          userId: req.session.userId,
          sessionVersion,
          currentVersion,
          sessionId: req.sessionID?.substring(0, 8) + '...'
        });
        return false;
      }

      return true;
    } catch (error) {
      logger.error('Failed to validate session version', {
        userId: req.session?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Increment user's session version (invalidates all existing sessions)
   */
  static async incrementUserVersion(userId: string, reason: string): Promise<number> {
    try {
      const versionKey = `${this.SESSION_VERSION_PREFIX}${userId}`;
      const newVersion = await redis.incr(versionKey);
      
      // Set TTL for version tracking
      await redis.expire(versionKey, this.SESSION_TTL);

      // Invalidate all existing sessions for this user
      await this.invalidateAllUserSessions(userId);

      logger.info('User session version incremented', {
        userId,
        newVersion,
        reason
      });

      return newVersion;
    } catch (error) {
      logger.error('Failed to increment user session version', {
        userId,
        reason,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get current session version for a user
   */
  static async getCurrentUserVersion(userId: string): Promise<number> {
    try {
      const versionKey = `${this.SESSION_VERSION_PREFIX}${userId}`;
      const version = await redis.get(versionKey);
      return version ? parseInt(version, 10) : 1;
    } catch (error) {
      logger.error('Failed to get user session version', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 1; // Default version
    }
  }

  /**
   * Track a user session in Redis
   */
  private static async trackUserSession(userId: string, sessionId: string, version: number): Promise<void> {
    try {
      const sessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      const sessionData = {
        sessionId,
        version,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString()
      };

      await redis.hset(sessionsKey, sessionId, JSON.stringify(sessionData));
      await redis.expire(sessionsKey, this.SESSION_TTL);
    } catch (error) {
      logger.error('Failed to track user session', {
        userId,
        sessionId: sessionId.substring(0, 8) + '...',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Invalidate all sessions for a user
   */
  static async invalidateAllUserSessions(userId: string): Promise<void> {
    try {
      const sessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      await redis.del(sessionsKey);

      logger.info('All user sessions invalidated', { userId });
    } catch (error) {
      logger.error('Failed to invalidate user sessions', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Invalidate a specific session
   */
  static async invalidateSession(req: Request, res: Response): Promise<void> {
    try {
      if (req.session && req.session.userId) {
        const userId = req.session.userId;
        const sessionId = req.sessionID;

        // Remove from Redis tracking
        const sessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
        await redis.hdel(sessionsKey, sessionId);

        // Destroy the session
        req.session.destroy((err) => {
          if (err) {
            logger.error('Failed to destroy session', { 
              userId, 
              sessionId: sessionId?.substring(0, 8) + '...',
              error: err.message 
            });
          }
        });

        // Clear session cookie
        res.clearCookie('connect.sid');

        logger.info('Session invalidated', {
          userId,
          sessionId: sessionId?.substring(0, 8) + '...'
        });
      }
    } catch (error) {
      logger.error('Failed to invalidate session', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Handle role change - increment version and invalidate sessions
   */
  static async handleRoleChange(userId: string, oldRole: string, newRole: string): Promise<void> {
    try {
      await this.incrementUserVersion(userId, `Role changed from ${oldRole} to ${newRole}`);
      
      logger.info('User role change processed', {
        userId,
        oldRole,
        newRole
      });
    } catch (error) {
      logger.error('Failed to handle role change', {
        userId,
        oldRole,
        newRole,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Handle password change - increment version and invalidate sessions
   */
  static async handlePasswordChange(userId: string): Promise<void> {
    try {
      await this.incrementUserVersion(userId, 'Password changed');
      
      logger.info('User password change processed', { userId });
    } catch (error) {
      logger.error('Failed to handle password change', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get session statistics for monitoring
   */
  static async getSessionStats(userId: string): Promise<{
    currentVersion: number;
    activeSessions: number;
    sessionDetails: Array<{
      sessionId: string;
      version: number;
      createdAt: string;
      lastActivity: string;
    }>;
  }> {
    try {
      const currentVersion = await this.getCurrentUserVersion(userId);
      const sessionsKey = `${this.USER_SESSIONS_PREFIX}${userId}`;
      const sessions = await redis.hgetall(sessionsKey);

      const sessionDetails = Object.entries(sessions).map(([sessionId, data]) => {
        const sessionData = JSON.parse(data);
        return {
          sessionId: sessionId.substring(0, 8) + '...',
          version: sessionData.version,
          createdAt: sessionData.createdAt,
          lastActivity: sessionData.lastActivity
        };
      });

      return {
        currentVersion,
        activeSessions: sessionDetails.length,
        sessionDetails
      };
    } catch (error) {
      logger.error('Failed to get session statistics', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {
        currentVersion: 1,
        activeSessions: 0,
        sessionDetails: []
      };
    }
  }
}

export default SessionVersioningService;
