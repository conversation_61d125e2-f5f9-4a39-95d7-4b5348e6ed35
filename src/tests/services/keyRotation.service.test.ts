import { KeyRotationService } from '../../services/keyRotation.service';
import { redis } from '../../config/redis';
import { logger } from '../../utils/logger';

// Mock Redis
jest.mock('../../config/redis', () => ({
  redis: {
    setex: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    keys: jest.fn(),
    del: jest.fn(),
    pipeline: jest.fn(() => ({
      del: jest.fn(),
      exec: jest.fn()
    }))
  }
}));

// Mock logger
jest.mock('../../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn()
  }
}));

describe('KeyRotationService', () => {
  const mockRedis = redis as jest.Mocked<typeof redis>;
  const mockLogger = logger as jest.Mocked<typeof logger>;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // 2022-01-01 00:00:00
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('rotateKey', () => {
    it('should generate and store new key', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      const keyId = await KeyRotationService.rotateKey();

      expect(keyId).toBeDefined();
      expect(typeof keyId).toBe('string');
      expect(keyId.length).toBeGreaterThan(0);

      // Should store the key with TTL
      expect(mockRedis.setex).toHaveBeenCalledWith(
        expect.stringContaining('jwt_key:'),
        expect.any(Number),
        expect.any(String)
      );

      // Should update current key ID
      expect(mockRedis.set).toHaveBeenCalledWith('jwt_current_key_id', keyId);

      expect(mockLogger.info).toHaveBeenCalledWith(
        'JWT key rotated successfully',
        expect.objectContaining({ keyId })
      );
    });

    it('should handle Redis errors during key rotation', async () => {
      mockRedis.setex.mockRejectedValue(new Error('Redis connection failed'));

      await expect(KeyRotationService.rotateKey())
        .rejects.toThrow('Redis connection failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to rotate JWT key',
        expect.objectContaining({ error: 'Redis connection failed' })
      );
    });

    it('should generate unique key IDs', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      const keyId1 = await KeyRotationService.rotateKey();
      const keyId2 = await KeyRotationService.rotateKey();

      expect(keyId1).not.toBe(keyId2);
    });
  });

  describe('getCurrentKey', () => {
    it('should return current key when available', async () => {
      const mockKeyId = 'test-key-id';
      const mockKeyData = {
        id: mockKeyId,
        secret: 'test-secret',
        createdAt: new Date().toISOString()
      };

      mockRedis.get
        .mockResolvedValueOnce(mockKeyId) // current key ID
        .mockResolvedValueOnce(JSON.stringify(mockKeyData)); // key data

      const currentKey = await KeyRotationService.getCurrentKey();

      expect(currentKey).toEqual(mockKeyData);
      expect(mockRedis.get).toHaveBeenCalledWith('jwt_current_key_id');
      expect(mockRedis.get).toHaveBeenCalledWith(`jwt_key:${mockKeyId}`);
    });

    it('should return null when no current key is set', async () => {
      mockRedis.get.mockResolvedValue(null);

      const currentKey = await KeyRotationService.getCurrentKey();

      expect(currentKey).toBeNull();
    });

    it('should return null when current key data is not found', async () => {
      mockRedis.get
        .mockResolvedValueOnce('missing-key-id')
        .mockResolvedValueOnce(null);

      const currentKey = await KeyRotationService.getCurrentKey();

      expect(currentKey).toBeNull();
    });

    it('should handle Redis errors gracefully', async () => {
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const currentKey = await KeyRotationService.getCurrentKey();

      expect(currentKey).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to get current JWT key',
        expect.objectContaining({ error: 'Redis connection failed' })
      );
    });

    it('should handle invalid JSON in key data', async () => {
      mockRedis.get
        .mockResolvedValueOnce('test-key-id')
        .mockResolvedValueOnce('invalid-json');

      const currentKey = await KeyRotationService.getCurrentKey();

      expect(currentKey).toBeNull();
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to parse JWT key data',
        expect.any(Object)
      );
    });
  });

  describe('getKeyById', () => {
    it('should return key by ID when it exists', async () => {
      const keyId = 'test-key-id';
      const mockKeyData = {
        id: keyId,
        secret: 'test-secret',
        createdAt: new Date().toISOString()
      };

      mockRedis.get.mockResolvedValue(JSON.stringify(mockKeyData));

      const key = await KeyRotationService.getKeyById(keyId);

      expect(key).toEqual(mockKeyData);
      expect(mockRedis.get).toHaveBeenCalledWith(`jwt_key:${keyId}`);
    });

    it('should return null when key does not exist', async () => {
      mockRedis.get.mockResolvedValue(null);

      const key = await KeyRotationService.getKeyById('non-existent-key');

      expect(key).toBeNull();
    });

    it('should handle Redis errors', async () => {
      mockRedis.get.mockRejectedValue(new Error('Redis connection failed'));

      const key = await KeyRotationService.getKeyById('test-key');

      expect(key).toBeNull();
    });
  });

  describe('forceRotation', () => {
    it('should force immediate key rotation', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      const keyId = await KeyRotationService.forceRotation('Security incident');

      expect(keyId).toBeDefined();
      expect(mockLogger.info).toHaveBeenCalledWith(
        'JWT key force rotated',
        expect.objectContaining({
          keyId,
          reason: 'Security incident'
        })
      );
    });

    it('should use default reason when none provided', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      await KeyRotationService.forceRotation();

      expect(mockLogger.info).toHaveBeenCalledWith(
        'JWT key force rotated',
        expect.objectContaining({
          reason: 'Manual rotation'
        })
      );
    });
  });

  describe('cleanupExpiredKeys', () => {
    it('should clean up expired keys', async () => {
      const mockKeys = ['jwt_key:key1', 'jwt_key:key2', 'jwt_key:key3'];
      const mockPipeline = {
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([])
      };

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      const result = await KeyRotationService.cleanupExpiredKeys();

      expect(result.processed).toBe(3);
      expect(result.deleted).toBe(3);
      expect(mockRedis.keys).toHaveBeenCalledWith('jwt_key:*');
      expect(mockPipeline.del).toHaveBeenCalledTimes(3);
      expect(mockPipeline.exec).toHaveBeenCalled();
    });

    it('should handle cleanup errors gracefully', async () => {
      mockRedis.keys.mockRejectedValue(new Error('Redis connection failed'));

      const result = await KeyRotationService.cleanupExpiredKeys();

      expect(result.processed).toBe(0);
      expect(result.deleted).toBe(0);
      expect(result.error).toBe('Failed to cleanup expired keys');
    });

    it('should exclude current key from cleanup', async () => {
      const currentKeyId = 'current-key';
      const mockKeys = [`jwt_key:${currentKeyId}`, 'jwt_key:old-key'];
      const mockPipeline = {
        del: jest.fn(),
        exec: jest.fn().mockResolvedValue([])
      };

      mockRedis.get.mockResolvedValue(currentKeyId);
      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.pipeline.mockReturnValue(mockPipeline as any);

      const result = await KeyRotationService.cleanupExpiredKeys();

      // Should only delete the old key, not the current one
      expect(mockPipeline.del).toHaveBeenCalledTimes(1);
      expect(mockPipeline.del).toHaveBeenCalledWith('jwt_key:old-key');
    });
  });

  describe('getRotationStats', () => {
    it('should return rotation statistics', async () => {
      const mockKeys = ['jwt_key:key1', 'jwt_key:key2'];
      const currentKeyId = 'key1';

      mockRedis.keys.mockResolvedValue(mockKeys);
      mockRedis.get.mockResolvedValue(currentKeyId);

      const stats = await KeyRotationService.getRotationStats();

      expect(stats).toEqual({
        totalKeys: 2,
        currentKeyId,
        timestamp: expect.any(String)
      });
    });

    it('should handle errors in stats collection', async () => {
      mockRedis.keys.mockRejectedValue(new Error('Redis connection failed'));

      const stats = await KeyRotationService.getRotationStats();

      expect(stats).toEqual({
        totalKeys: 0,
        currentKeyId: null,
        timestamp: expect.any(String),
        error: 'Failed to retrieve rotation statistics'
      });
    });
  });

  describe('Key Generation', () => {
    it('should generate cryptographically secure keys', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      const keyId1 = await KeyRotationService.rotateKey();
      const keyId2 = await KeyRotationService.rotateKey();

      // Keys should be different
      expect(keyId1).not.toBe(keyId2);

      // Keys should have sufficient entropy (length check)
      expect(keyId1.length).toBeGreaterThanOrEqual(16);
      expect(keyId2.length).toBeGreaterThanOrEqual(16);
    });
  });

  describe('Automatic Rotation', () => {
    it('should start automatic rotation', () => {
      const startSpy = jest.spyOn(KeyRotationService, 'startAutomaticRotation');
      
      KeyRotationService.startAutomaticRotation();

      expect(startSpy).toHaveBeenCalled();
    });

    it('should stop automatic rotation', () => {
      const stopSpy = jest.spyOn(KeyRotationService, 'stopAutomaticRotation');
      
      KeyRotationService.stopAutomaticRotation();

      expect(stopSpy).toHaveBeenCalled();
    });
  });

  describe('Error Recovery', () => {
    it('should handle partial Redis failures during rotation', async () => {
      // Simulate key storage success but current key ID update failure
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockRejectedValue(new Error('Failed to update current key ID'));

      await expect(KeyRotationService.rotateKey())
        .rejects.toThrow('Failed to update current key ID');

      expect(mockLogger.error).toHaveBeenCalled();
    });

    it('should handle concurrent rotation attempts gracefully', async () => {
      mockRedis.setex.mockResolvedValue('OK');
      mockRedis.set.mockResolvedValue('OK');

      // Simulate concurrent rotations
      const rotations = Promise.all([
        KeyRotationService.rotateKey(),
        KeyRotationService.rotateKey(),
        KeyRotationService.rotateKey()
      ]);

      const keyIds = await rotations;

      // All rotations should succeed with unique IDs
      expect(keyIds).toHaveLength(3);
      expect(new Set(keyIds).size).toBe(3); // All unique
    });
  });
});
