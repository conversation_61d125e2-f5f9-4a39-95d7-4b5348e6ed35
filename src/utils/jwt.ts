import * as jwt from 'jsonwebtoken';
import { JwtPayload, SignOptions, VerifyOptions } from 'jsonwebtoken';
import type { StringValue } from 'ms';
import { JWT_SECRET, JWT_EXPIRES_IN, JWT_REFRESH_EXPIRES_IN } from '../config';
import { KeyRotationService } from '../services/keyRotation.service';
import { logger } from '../utils/secureLogger';

/**
 * JWT payload interface
 */
export interface JwtTokenPayload {
  id: string;
  email: string;
  role: string;
}

/**
 * JWT utility class with methods for signing and verifying tokens
 */
export class JwtUtils {
  private static readonly secret = JWT_SECRET;
  private static readonly defaultExpiresIn = JWT_EXPIRES_IN;

  /**
   * Sign a JWT token with the given payload using key rotation
   * @param payload - The payload to include in the token
   * @param options - Additional signing options
   * @returns The signed JWT token
   */
  static async sign(payload: JwtTokenPayload, options?: Partial<SignOptions>): Promise<string> {
    try {
      // Try to get current key from rotation service
      const currentKey = await KeyRotationService.getCurrentKey();

      let secret = this.secret;
      let keyId = 'static';

      if (currentKey) {
        secret = currentKey.secret;
        keyId = currentKey.id;
      } else {
        logger.warn('Key rotation service unavailable, using static secret');
      }

      const signOptions: SignOptions = {
        expiresIn: this.defaultExpiresIn as StringValue,
        issuer: 'secure-backend',
        audience: 'secure-backend-users',
        keyid: keyId, // Include key ID in token header
        ...options,
      };

      return jwt.sign(payload, secret, signOptions);
    } catch (error) {
      logger.error('JWT signing failed, falling back to static secret', { error });
      // Fallback to static secret if key rotation fails
      const signOptions: SignOptions = {
        expiresIn: this.defaultExpiresIn as StringValue,
        issuer: 'secure-backend',
        audience: 'secure-backend-users',
        keyid: 'static',
        ...options,
      };
      return jwt.sign(payload, this.secret, signOptions);
    }
  }

  /**
   * Verify and decode a JWT token with key rotation support
   * @param token - The JWT token to verify
   * @param options - Additional verification options
   * @returns The decoded token payload
   * @throws Error if token is invalid or expired
   */
  static async verify(token: string, options?: VerifyOptions): Promise<JwtTokenPayload> {
    const verifyOptions: VerifyOptions = {
      issuer: 'secure-backend',
      audience: 'secure-backend-users',
      ...options,
    };

    try {
      // First, try to decode the token header to get the key ID
      const decodedHeader = jwt.decode(token, { complete: true });
      const keyId = decodedHeader?.header?.kid;

      let secret = this.secret;

      if (keyId && keyId !== 'static') {
        // Try to get the specific key for verification
        const keyData = await KeyRotationService.getKey(keyId);
        if (keyData) {
          secret = keyData.key;
        } else {
          logger.warn('Token signed with unknown key, trying current key', { keyId });
          // Try current key as fallback
          const currentKey = await KeyRotationService.getCurrentKey();
          if (currentKey) {
            secret = currentKey.secret;
          }
        }
      }

      const decoded = jwt.verify(token, secret, verifyOptions) as JwtPayload;

      // Validate that the decoded token has the expected structure
      if (!decoded.id || !decoded.email || !decoded.role) {
        throw new Error('Invalid token payload structure');
      }

      return {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
      };
    } catch (error) {
      // If verification fails with rotated key, try static secret as fallback
      if (error instanceof jwt.JsonWebTokenError) {
        try {
          logger.warn('Token verification failed with rotated key, trying static secret');
          const decoded = jwt.verify(token, this.secret, verifyOptions) as JwtPayload;

          if (!decoded.id || !decoded.email || !decoded.role) {
            throw new Error('Invalid token payload structure');
          }

          return {
            id: decoded.id,
            email: decoded.email,
            role: decoded.role,
          };
        } catch (fallbackError) {
          logger.error('Token verification failed with both rotated and static keys');
          throw fallbackError;
        }
      }
      throw error;
    }
  }

  /**
   * Decode a JWT token without verifying the signature
   * @param token - The JWT token to decode
   * @returns The decoded token payload or null if invalid
   */
  static decode(token: string): JwtTokenPayload | null {
    try {
      const decoded = jwt.decode(token) as JwtPayload;

      if (!decoded || !decoded.id || !decoded.email || !decoded.role) {
        return null;
      }

      return {
        id: decoded.id,
        email: decoded.email,
        role: decoded.role,
      };
    } catch {
      return null;
    }
  }

  /**
   * Check if a JWT token is expired
   * @param token - The JWT token to check
   * @returns True if the token is expired, false otherwise
   */
  static isExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JwtPayload;

      if (!decoded || !decoded.exp) {
        return true;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch {
      return true;
    }
  }

  /**
   * Get the remaining time until token expiration
   * @param token - The JWT token to check
   * @returns The remaining time in seconds, or 0 if expired/invalid
   */
  static getTimeToExpiry(token: string): number {
    try {
      const decoded = jwt.decode(token) as JwtPayload;

      if (!decoded || !decoded.exp) {
        return 0;
      }

      const currentTime = Math.floor(Date.now() / 1000);
      const remainingTime = decoded.exp - currentTime;

      return Math.max(0, remainingTime);
    } catch {
      return 0;
    }
  }

  /**
   * Create a refresh token with extended expiration
   * @param payload - The payload to include in the token
   * @param expiresIn - Custom expiration time (defaults to configuration)
   * @returns The signed refresh token
   */
  static async signRefreshToken(
    payload: JwtTokenPayload,
    expiresIn: StringValue = JWT_REFRESH_EXPIRES_IN as StringValue,
  ): Promise<string> {
    return await this.sign(payload, { expiresIn });
  }

  /**
   * Get token expiration times in milliseconds for cookie configuration
   */
  static getTokenExpiryMs(): { access: number; refresh: number } {
    // Convert time strings to milliseconds
    const parseTime = (timeStr: string): number => {
      const unit = timeStr.slice(-1);
      const value = parseInt(timeStr.slice(0, -1));

      switch (unit) {
        case 's':
          return value * 1000;
        case 'm':
          return value * 60 * 1000;
        case 'h':
          return value * 60 * 60 * 1000;
        case 'd':
          return value * 24 * 60 * 60 * 1000;
        default:
          return value;
      }
    };

    return {
      access: parseTime(JWT_EXPIRES_IN),
      refresh: parseTime(JWT_REFRESH_EXPIRES_IN),
    };
  }

  /**
   * Extract token ID for blacklist tracking
   * @param token - JWT token
   * @returns Token JTI (JWT ID) or null if not found
   */
  static getTokenId(token: string): string | null {
    try {
      const decoded = jwt.decode(token) as JwtPayload;
      return decoded?.jti || null;
    } catch {
      return null;
    }
  }

  /**
   * Get token expiration time in milliseconds from token ID
   * @param tokenId - JWT token ID (jti)
   * @returns Expiration time in milliseconds
   */
  static getTokenExpiry(tokenId: string): number {
    try {
      // For now, return a default expiration time based on token type
      // In a real implementation, you might store token metadata in Redis
      // or decode the actual token to get the expiration

      // Default to access token expiration (15 minutes)
      return Date.now() + (15 * 60 * 1000);
    } catch {
      return Date.now() + (15 * 60 * 1000); // 15 minutes fallback
    }
  }
}

/**
 * Convenience functions for backward compatibility and simpler usage
 */

/**
 * Sign a JWT token
 * @param payload - The payload to include in the token
 * @param expiresIn - Optional custom expiration time
 * @returns The signed JWT token
 */
export const signToken = (payload: JwtTokenPayload, expiresIn?: StringValue): string => {
  return JwtUtils.sign(payload, expiresIn ? { expiresIn } : undefined);
};

/**
 * Verify a JWT token
 * @param token - The JWT token to verify
 * @returns The decoded token payload
 * @throws Error if token is invalid or expired
 */
export const verifyToken = (token: string): JwtTokenPayload => {
  return JwtUtils.verify(token);
};

/**
 * Decode a JWT token without verification
 * @param token - The JWT token to decode
 * @returns The decoded token payload or null if invalid
 */
export const decodeToken = (token: string): JwtTokenPayload | null => {
  return JwtUtils.decode(token);
};

export default JwtUtils;
